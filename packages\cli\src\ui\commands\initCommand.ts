/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { SlashCommand, CommandKind, CommandContext, MessageActionReturn } from '../commands/types.js';
import * as fs from 'node:fs';
import * as path from 'node:path';
import * as os from 'node:os';
const GEMINI_DIR = '.s647';

/**
 * Initialize S647 configuration in the current directory
 */
export const initCommand: SlashCommand = {
  name: 'init',
  description: 'Initialize S647 configuration in current directory',
  kind: CommandKind.BUILT_IN,
  action: async (context: CommandContext, args: string): Promise<MessageActionReturn> => {
    const isGlobal = args.includes('--global');
    const targetDir = isGlobal ? os.homedir() : process.cwd();
    const s647Dir = path.join(targetDir, GEMINI_DIR);

    try {
      // Create .s647 directory
      if (!fs.existsSync(s647Dir)) {
        fs.mkdirSync(s647Dir, { recursive: true });
        console.log(`✅ Created ${GEMINI_DIR} directory`);
      } else {
        console.log(`📁 ${GEMINI_DIR} directory already exists`);
      }

      // Copy example files
      await copyExampleFiles(s647Dir);

      const location = isGlobal ? 'globally' : 'in current directory';
      const message = `🎉 S647 initialized ${location}!\n\n` +
             `📁 Configuration directory: ${s647Dir}\n` +
             `📝 Edit .env file to add your API keys\n` +
             `📚 Check GEMINI.md for project context\n` +
             `🔧 See examples/ for usage patterns\n\n` +
             `Next steps:\n` +
             `1. Add your API keys to .env\n` +
             `2. Customize GEMINI.md with project info\n` +
             `3. Run 's647' to start!`;

      return {
        type: 'message',
        messageType: 'info',
        content: message
      };
    } catch (error) {
      return {
        type: 'message',
        messageType: 'error',
        content: `❌ Failed to initialize S647: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  },
};

/**
 * Copy example files to the .s647 directory
 */
async function copyExampleFiles(s647Dir: string): Promise<void> {
  const exampleFiles = [
    { source: '.env.example', dest: '.env', description: 'Environment configuration' },
    { source: 'GEMINI.md', dest: 'GEMINI.md', description: 'Project context file' },
    { source: 'PRINCIPLES.md', dest: 'PRINCIPLES.md', description: 'AI principles guide' },
  ];

  // Find the package root (where example files are located)
  const packageRoot = findPackageRoot();
  
  for (const file of exampleFiles) {
    const sourcePath = path.join(packageRoot, file.source);
    const destPath = path.join(s647Dir, file.dest);
    
    try {
      if (fs.existsSync(sourcePath)) {
        if (!fs.existsSync(destPath)) {
          fs.copyFileSync(sourcePath, destPath);
          console.log(`📄 Created ${file.dest} (${file.description})`);
        } else {
          console.log(`📄 ${file.dest} already exists, skipping`);
        }
      } else {
        console.warn(`⚠️  Source file not found: ${sourcePath}`);
      }
    } catch (error) {
      console.warn(`⚠️  Failed to copy ${file.source}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Create examples directory
  const examplesDir = path.join(s647Dir, 'examples');
  if (!fs.existsSync(examplesDir)) {
    fs.mkdirSync(examplesDir, { recursive: true });
    
    // Create example files
    const exampleContent = `# S647 Usage Examples

## Basic Commands
\`\`\`bash
s647 --prompt "Analyze this codebase"
s647 --prompt "Create a React component for user login"
s647 --prompt "Help me debug this error"
\`\`\`

## Interactive Mode
\`\`\`bash
s647
> "What does this function do?"
> "Refactor this code to use TypeScript"
> "Generate unit tests for this module"
\`\`\`

## Project-Specific Tasks
\`\`\`bash
s647 --prompt "Review the authentication system"
s647 --prompt "Optimize database queries"
s647 --prompt "Add error handling to API endpoints"
\`\`\`
`;
    
    fs.writeFileSync(path.join(examplesDir, 'README.md'), exampleContent);
    console.log(`📚 Created examples/README.md`);
  }
}

/**
 * Find the package root directory (where example files are located)
 */
function findPackageRoot(): string {
  let currentDir = __dirname;
  
  // Go up directories until we find package.json or reach root
  while (currentDir !== path.dirname(currentDir)) {
    const packageJsonPath = path.join(currentDir, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      // Check if this is the main package (has .env.example)
      const envExamplePath = path.join(currentDir, '.env.example');
      if (fs.existsSync(envExamplePath)) {
        return currentDir;
      }
    }
    currentDir = path.dirname(currentDir);
  }
  
  // Fallback: assume we're in a development environment
  return process.cwd();
}
