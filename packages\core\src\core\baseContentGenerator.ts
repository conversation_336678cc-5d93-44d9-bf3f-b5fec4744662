/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

export interface ContentPart {
  text?: string;
  inlineData?: {
    mimeType: string;
    data: string;
  };
  fileData?: {
    mimeType: string;
    fileUri: string;
  };
}

export interface GenerateContentRequest {
  contents: Array<{
    role: 'user' | 'model';
    parts: ContentPart[];
  }>;
  generationConfig?: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxOutputTokens?: number;
    stopSequences?: string[];
  };
  safetySettings?: Array<{
    category: string;
    threshold: string;
  }>;
  tools?: Array<{
    functionDeclarations?: Array<{
      name: string;
      description: string;
      parameters?: object;
    }>;
  }>;
}

export interface GenerateContentResponse {
  candidates?: Array<{
    content?: {
      parts: ContentPart[];
      role: string;
    };
    finishReason?: string;
    index?: number;
    safetyRatings?: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  promptFeedback?: {
    safetyRatings?: Array<{
      category: string;
      probability: string;
    }>;
    blockReason?: string;
  };
  usageMetadata?: {
    promptTokenCount?: number;
    candidatesTokenCount?: number;
    totalTokenCount?: number;
  };
}

export interface EmbeddingRequest {
  content: {
    parts: ContentPart[];
  };
}

export interface EmbeddingResponse {
  embedding: {
    values: number[];
  };
}

export interface BaseContentGenerator {
  generateContent(
    request: GenerateContentRequest,
  ): Promise<GenerateContentResponse>;
  embedContent(request: EmbeddingRequest): Promise<EmbeddingResponse>;
}
