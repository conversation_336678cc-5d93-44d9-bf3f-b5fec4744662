# Gemini CLI Environment Variables Example
# Copy this file to .env and fill in your API keys

# =============================================================================
# PROVIDER CONFIGURATION
# =============================================================================

# Default provider to use (auto-detected if not set)
# Options: google, openai, anthropic, mistral, openrouter, custom
GEMINI_PROVIDER=openrouter

# Default model to use (provider-specific)
GEMINI_MODEL=qwen/qwen3-coder:free

# =============================================================================
# API KEYS - Set the one(s) you want to use
# =============================================================================

# Google Gemini API Key (get from https://aistudio.google.com/apikey)
GEMINI_API_KEY=your_gemini_api_key_here

# Google Cloud API Key for Vertex AI (get from Google Cloud Console)
GOOGLE_API_KEY=your_google_cloud_api_key_here
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1

# OpenAI API Key (get from https://platform.openai.com/api-keys)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key (get from https://console.anthropic.com/)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Mistral API Key (get from https://console.mistral.ai/)
MISTRAL_API_KEY=your_mistral_api_key_here

# OpenRouter API Key (get from https://openrouter.ai/keys)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Custom Provider Settings
CUSTOM_API_KEY=your_custom_api_key_here
CUSTOM_BASE_URL=https://your-custom-endpoint.com/v1

# =============================================================================
# OPTIONAL SETTINGS
# =============================================================================

# Proxy settings
HTTPS_PROXY=http://proxy.example.com:8080
HTTP_PROXY=http://proxy.example.com:8080

# Disable browser launch for OAuth
NO_BROWSER=false

# Debug mode
DEBUG=false

# =============================================================================
# USAGE EXAMPLES
# =============================================================================
#
# 1. Use OpenAI by default:
#    GEMINI_PROVIDER=openai
#    OPENAI_API_KEY=sk-...
#    GEMINI_MODEL=gpt-4
#
# 2. Use Anthropic by default:
#    GEMINI_PROVIDER=anthropic
#    ANTHROPIC_API_KEY=sk-ant-...
#    GEMINI_MODEL=claude-3-5-sonnet-20241022
#
# 3. Use OpenRouter by default:
#    GEMINI_PROVIDER=openrouter
#    OPENROUTER_API_KEY=sk-or-...
#    GEMINI_MODEL=qwen/qwen3-coder:free
#
# 4. Auto-detect provider (based on available API keys):
#    # Just set your API key, provider will be auto-detected
#    OPENAI_API_KEY=sk-...
#
# 5. Override via command line:
#    gemini --provider anthropic --model claude-3-5-sonnet "Hello"
