# S647 🚀

[![NPM Version](https://img.shields.io/npm/v/@inkbytefo/s647)](https://www.npmjs.com/package/@inkbytefo/s647)
[![NPM Downloads](https://img.shields.io/npm/dm/@inkbytefo/s647)](https://www.npmjs.com/package/@inkbytefo/s647)

> **Your smart AI assistant, now in the terminal! 🤖**

S647 is an advanced AI CLI tool that understands your code, enhances your projects, and automates your workflows. Forked from Google's Gemini CLI with enhanced multi-provider AI support.

## What can it do? ✨

- 🔍 **Analyzes large codebases** - Understands your project in seconds
- 🎨 **Creates apps from PDFs** - Generates code from sketches
- ⚡ **Automates workflows** - Git, PRs, deployments made easy
- 🔌 **Multi-AI support** - Gemini, OpenAI, Claude, Mistral & more

## Quick Start 🏃‍♂️

```bash
# Try it instantly
npx @inkbytefo/s647

# Or install globally
npm install -g @inkbytefo/s647
s647
```

**Set your API key:**

Option 1 - Environment variables:
```bash
export GEMINI_API_KEY="your_key"
# or
export OPENAI_API_KEY="your_key"
```

Option 2 - Config file (recommended):
```bash
# Create global config
mkdir ~/.s647
echo "OPENAI_API_KEY=your_key_here" > ~/.s647/.env

# Or project-specific config
mkdir .s647
echo "OPENAI_API_KEY=your_key_here" > .s647/.env
```

That's it! 🎉

## Supported AI Providers 🤖

- **Gemini** - Get your key from [Google AI Studio](https://aistudio.google.com/apikey)
- **OpenAI** - GPT-4, GPT-3.5 and more
- **Anthropic** - Claude models
- **Mistral** - Latest Mistral models
- **OpenRouter** - Access to multiple providers
- **Custom** - Your own endpoints

Just set the API key and S647 auto-detects the provider! 🎯

## What can you do? 💡

```bash
s647
> "Create a Discord bot from this FAQ.md file"
> "Analyze this codebase and explain the architecture"
> "Help me fix this bug in the authentication system"
> "Generate a React component from this design mockup"
```

## Links 🔗

- 📚 [Documentation](./docs/index.md)
- 🐛 [Troubleshooting](./docs/troubleshooting.md)
- 🤝 [Contributing](./CONTRIBUTING.md)
- 🗺️ [Roadmap](./ROADMAP.md)

---

**Built with ❤️ by [inkbytefo](https://github.com/inkbytefo)**

*Based on Google's Gemini CLI with enhanced multi-provider support*
