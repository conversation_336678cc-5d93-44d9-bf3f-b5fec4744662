/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

export enum ModelProvider {
  GOOGLE = 'google',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  OPENROUTER = 'openrouter',
  MISTRAL = 'mistral',
  CUSTOM = 'custom',
}

export interface ModelConfig {
  provider: ModelProvider;
  model: string;
  apiKey?: string;
  baseUrl?: string;
  headers?: Record<string, string>;
  temperature?: number;
  maxTokens?: number;
}

export interface ProviderConfig {
  name: string;
  baseUrl: string;
  apiKeyEnv: string;
  modelPrefix?: string;
  headers?: Record<string, string>;
}

export const PROVIDER_CONFIGS: Record<ModelProvider, ProviderConfig> = {
  [ModelProvider.GOOGLE]: {
    name: 'Google',
    baseUrl: 'https://generativelanguage.googleapis.com',
    apiKeyEnv: 'GEMINI_API_KEY',
  },
  [ModelProvider.OPENAI]: {
    name: 'OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    apiKeyEnv: 'OPENAI_API_KEY',
  },
  [ModelProvider.ANTHROPIC]: {
    name: 'Anthropic',
    baseUrl: 'https://api.anthropic.com',
    apiKeyEnv: 'ANTHROPIC_API_KEY',
  },
  [ModelProvider.OPENROUTER]: {
    name: 'OpenRouter',
    baseUrl: 'https://openrouter.ai/api/v1',
    apiKeyEnv: 'OPENROUTER_API_KEY',
  },
  [ModelProvider.MISTRAL]: {
    name: 'Mistral',
    baseUrl: 'https://api.mistral.ai/v1',
    apiKeyEnv: 'MISTRAL_API_KEY',
  },
  [ModelProvider.CUSTOM]: {
    name: 'Custom',
    baseUrl: '',
    apiKeyEnv: 'CUSTOM_API_KEY',
  },
};

export function detectProviderFromModel(model: string): ModelProvider {
  const lowerModel = model.toLowerCase();

  if (lowerModel.startsWith('gemini-') || lowerModel.includes('gemini')) {
    return ModelProvider.GOOGLE;
  }

  if (lowerModel.startsWith('gpt-') || lowerModel.includes('text-davinci')) {
    return ModelProvider.OPENAI;
  }

  if (lowerModel.startsWith('claude-') || lowerModel.includes('claude')) {
    return ModelProvider.ANTHROPIC;
  }

  if (lowerModel.startsWith('mistral-') || lowerModel.includes('mistral')) {
    return ModelProvider.MISTRAL;
  }

  // OpenRouter format: provider/model
  if (model.includes('/')) {
    return ModelProvider.OPENROUTER;
  }

  return ModelProvider.GOOGLE; // Default fallback
}
