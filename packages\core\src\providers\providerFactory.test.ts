/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ProviderFactory } from './providerFactory.js';
import { OpenAIProvider } from './openaiProvider.js';
import { AuthType, ContentGeneratorConfig } from '../core/contentGenerator.js';
import { Config } from '../config/config.js';

// Mock the dependencies
vi.mock('./openaiProvider.js');
vi.mock('../code_assist/codeAssist.js');
vi.mock('@google/genai');

describe('ProviderFactory', () => {
  let mockConfig: ContentGeneratorConfig;
  let mockGcConfig: Config;

  beforeEach(() => {
    mockConfig = {
      model: 'gpt-3.5-turbo',
      apiKey: 'test-api-key',
      authType: AuthType.USE_OPENAI,
    };

    mockGcConfig = {} as Config;
    vi.clearAllMocks();
  });

  describe('createProvider', () => {
    it('should create OpenAI provider', async () => {
      const mockProvider = {} as OpenAIProvider;
      vi.mocked(OpenAIProvider).mockImplementation(() => mockProvider);

      const result = await ProviderFactory.createProvider({
        config: mockConfig,
        gcConfig: mockGcConfig,
      });

      expect(OpenAIProvider).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        headers: undefined,
        proxy: undefined,
      });
      expect(result).toBe(mockProvider);
    });

    it('should create Anthropic provider', async () => {
      mockConfig.authType = AuthType.USE_ANTHROPIC;
      const mockProvider = {} as OpenAIProvider;
      vi.mocked(OpenAIProvider).mockImplementation(() => mockProvider);

      const result = await ProviderFactory.createProvider({
        config: mockConfig,
        gcConfig: mockGcConfig,
      });

      expect(OpenAIProvider).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.anthropic.com',
        model: 'gpt-3.5-turbo',
        headers: undefined,
        proxy: undefined,
      });
      expect(result).toBe(mockProvider);
    });

    it('should create Mistral provider', async () => {
      mockConfig.authType = AuthType.USE_MISTRAL;
      const mockProvider = {} as OpenAIProvider;
      vi.mocked(OpenAIProvider).mockImplementation(() => mockProvider);

      const result = await ProviderFactory.createProvider({
        config: mockConfig,
        gcConfig: mockGcConfig,
      });

      expect(OpenAIProvider).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        baseUrl: 'https://api.mistral.ai/v1',
        model: 'gpt-3.5-turbo',
        headers: undefined,
        proxy: undefined,
      });
      expect(result).toBe(mockProvider);
    });

    it('should create OpenRouter provider', async () => {
      mockConfig.authType = AuthType.USE_OPENROUTER;
      const mockProvider = {} as OpenAIProvider;
      vi.mocked(OpenAIProvider).mockImplementation(() => mockProvider);

      const result = await ProviderFactory.createProvider({
        config: mockConfig,
        gcConfig: mockGcConfig,
      });

      expect(OpenAIProvider).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        baseUrl: 'https://openrouter.ai/api/v1',
        model: 'gpt-3.5-turbo',
        headers: undefined,
        proxy: undefined,
      });
      expect(result).toBe(mockProvider);
    });

    it('should create Custom provider with environment variable', async () => {
      process.env.CUSTOM_BASE_URL = 'https://custom.api.com/v1';
      mockConfig.authType = AuthType.USE_CUSTOM;
      const mockProvider = {} as OpenAIProvider;
      vi.mocked(OpenAIProvider).mockImplementation(() => mockProvider);

      const result = await ProviderFactory.createProvider({
        config: mockConfig,
        gcConfig: mockGcConfig,
      });

      expect(OpenAIProvider).toHaveBeenCalledWith({
        apiKey: 'test-api-key',
        baseUrl: 'https://custom.api.com/v1',
        model: 'gpt-3.5-turbo',
        headers: undefined,
        proxy: undefined,
      });
      expect(result).toBe(mockProvider);

      delete process.env.CUSTOM_BASE_URL;
    });

    it('should throw error for unsupported auth type', async () => {
      mockConfig.authType = 'unsupported' as AuthType;

      await expect(
        ProviderFactory.createProvider({
          config: mockConfig,
          gcConfig: mockGcConfig,
        }),
      ).rejects.toThrow(
        'Error creating provider: Unsupported authType: unsupported',
      );
    });
  });

  describe('getSupportedProviders', () => {
    it('should return list of supported providers', () => {
      const providers = ProviderFactory.getSupportedProviders();
      expect(providers).toEqual([
        'google',
        'openai',
        'anthropic',
        'mistral',
        'openrouter',
        'custom',
      ]);
    });
  });

  describe('getProviderFromAuthType', () => {
    it('should return correct provider for each auth type', () => {
      expect(ProviderFactory.getProviderFromAuthType(AuthType.USE_OPENAI)).toBe(
        'openai',
      );
      expect(
        ProviderFactory.getProviderFromAuthType(AuthType.USE_ANTHROPIC),
      ).toBe('anthropic');
      expect(
        ProviderFactory.getProviderFromAuthType(AuthType.USE_MISTRAL),
      ).toBe('mistral');
      expect(
        ProviderFactory.getProviderFromAuthType(AuthType.USE_OPENROUTER),
      ).toBe('openrouter');
      expect(ProviderFactory.getProviderFromAuthType(AuthType.USE_CUSTOM)).toBe(
        'custom',
      );
      expect(ProviderFactory.getProviderFromAuthType(AuthType.USE_GEMINI)).toBe(
        'google',
      );
      expect(
        ProviderFactory.getProviderFromAuthType(AuthType.LOGIN_WITH_GOOGLE),
      ).toBe('google');
    });
  });
});
