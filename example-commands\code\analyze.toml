# Code Analysis Command
# Usage: /code:analyze
# This command provides comprehensive analysis of code in the current context

description = "Perform thorough analysis of the code in context, including quality, patterns, and improvement opportunities."

prompt = """
Please perform a comprehensive analysis of the code provided in the current context.

Your analysis should cover:

## Code Quality Assessment
- **Style Consistency**: Check adherence to project conventions
- **Architecture Patterns**: Identify architectural decisions and patterns
- **Error Handling**: Evaluate error handling approaches
- **Security Considerations**: Identify potential security issues

## Technical Analysis
- **Dependencies**: List and evaluate external dependencies
- **Performance**: Identify potential performance bottlenecks
- **Maintainability**: Assess code maintainability and readability
- **Test Coverage**: Evaluate existing test coverage and quality

## Improvement Opportunities
- **Refactoring Suggestions**: Identify areas for improvement
- **Best Practices**: Suggest adherence to best practices
- **Documentation**: Assess and suggest documentation improvements
- **Optimization**: Identify optimization opportunities

## Recommendations
- **Priority Actions**: List high-priority improvements
- **Technical Debt**: Identify technical debt areas
- **Future Considerations**: Suggest long-term improvements

Please provide specific, actionable recommendations with clear reasoning for each suggestion.
"""
