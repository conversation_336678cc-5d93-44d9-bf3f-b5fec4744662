/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { Colors } from '../colors.js';
import { RadioButtonSelect } from './shared/RadioButtonSelect.js';
import { LoadedSettings, SettingScope } from '../../config/settings.js';
import { AuthType } from '@inkbytefo/s647-core';
import {
  validateAuthMethod,
  getAllProviderStatuses,
  ProviderStatus,
  suggestBestProvider,
} from '../../config/auth.js';

interface AuthDialogProps {
  onSelect: (authMethod: AuthType | undefined, scope: SettingScope) => void;
  settings: LoadedSettings;
  initialErrorMessage?: string | null;
}

function parseDefaultAuthType(
  defaultAuthType: string | undefined,
): AuthType | null {
  if (
    defaultAuthType &&
    Object.values(AuthType).includes(defaultAuthType as AuthType)
  ) {
    return defaultAuthType as AuthType;
  }
  return null;
}

export function AuthDialog({
  onSelect,
  settings,
  initialErrorMessage,
}: AuthDialogProps): React.JSX.Element {
  const [errorMessage, setErrorMessage] = useState<string | null>(
    initialErrorMessage || null,
  );
  const [providerStatuses, setProviderStatuses] = useState<ProviderStatus[]>(
    [],
  );
  const [loading, setLoading] = useState(true);
  const [suggestedProvider, setSuggestedProvider] = useState<
    string | undefined
  >();

  useEffect(() => {
    const loadProviderStatuses = async () => {
      try {
        const statuses = await getAllProviderStatuses();
        setProviderStatuses(statuses);

        const suggested = await suggestBestProvider();
        setSuggestedProvider(suggested);

        // Show helpful message about .env configuration
        if (process.env.GEMINI_PROVIDER && suggested) {
          setErrorMessage(
            `Found .env configuration for ${suggested}. This provider is recommended.`,
          );
        } else if (statuses.some((s) => s.available)) {
          const availableCount = statuses.filter((s) => s.available).length;
          setErrorMessage(
            `Found ${availableCount} available provider(s) with valid API keys.`,
          );
        } else {
          setErrorMessage(
            'No valid API keys found. You can use Google OAuth or add API keys to your .env file.',
          );
        }
      } catch (error) {
        setErrorMessage(`Error loading provider information: ${error}`);
      } finally {
        setLoading(false);
      }
    };

    loadProviderStatuses();
  }, []);

  const items = providerStatuses.map((status) => {
    const statusIcon = status.available ? '✓' : status.hasApiKey ? '⚠' : '✗';

    return {
      label: `${statusIcon} ${status.displayName}`,
      value: status.authType,
      description: status.available
        ? status.description
        : status.errorMessage || 'Not available',
      available: status.available,
      recommended: status.provider === suggestedProvider,
    };
  });

  const initialAuthIndex = items.findIndex((item) => {
    // First check if there's a recommended provider
    if (item.recommended) {
      return true;
    }

    // Then check user's current selection
    if (settings.merged.selectedAuthType) {
      return item.value === settings.merged.selectedAuthType;
    }

    // Check environment default
    const defaultAuthType = parseDefaultAuthType(
      process.env.GEMINI_DEFAULT_AUTH_TYPE,
    );
    if (defaultAuthType) {
      return item.value === defaultAuthType;
    }

    // Default to first available provider or Google OAuth
    return item.available || item.value === AuthType.LOGIN_WITH_GOOGLE;
  });

  const handleAuthSelect = (authMethod: AuthType) => {
    const error = validateAuthMethod(authMethod);
    if (error) {
      setErrorMessage(error);
    } else {
      setErrorMessage(null);
      onSelect(authMethod, SettingScope.User);
    }
  };

  useInput((_input, key) => {
    if (key.escape) {
      // Prevent exit if there is an error message.
      // This means they user is not authenticated yet.
      if (errorMessage) {
        return;
      }
      if (settings.merged.selectedAuthType === undefined) {
        // Prevent exiting if no auth method is set
        setErrorMessage(
          'You must select an auth method to proceed. Press Ctrl+C twice to exit.',
        );
        return;
      }
      onSelect(undefined, SettingScope.User);
    }
  });

  if (loading) {
    return (
      <Box
        borderStyle="round"
        borderColor={Colors.Gray}
        flexDirection="column"
        padding={1}
        width="100%"
      >
        <Text bold>Loading providers...</Text>
        <Box marginTop={1}>
          <Text>Checking available authentication methods...</Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      borderStyle="round"
      borderColor={Colors.Gray}
      flexDirection="column"
      padding={1}
      width="100%"
    >
      <Text bold>Authentication Setup</Text>
      <Box marginTop={1}>
        <Text>Choose your preferred authentication method:</Text>
      </Box>

      {/* Provider status legend */}
      <Box marginTop={1} flexDirection="column">
        <Text dimColor>Status: ✓ Available ⚠ Invalid Key ✗ Missing Key</Text>
      </Box>

      <Box marginTop={1}>
        <RadioButtonSelect
          items={items.map((item) => ({
            label: item.label,
            value: item.value,
          }))}
          initialIndex={Math.max(0, initialAuthIndex)}
          onSelect={handleAuthSelect}
          isFocused={true}
        />
      </Box>

      {/* Show additional info for selected provider */}
      <Box marginTop={1} flexDirection="column">
        {items.map(
          (item, index) =>
            index === Math.max(0, initialAuthIndex) && (
              <Box key={item.value} flexDirection="column">
                <Text dimColor>{item.description}</Text>
                {!item.available &&
                  item.value !== AuthType.LOGIN_WITH_GOOGLE && (
                    <Text color={Colors.AccentYellow}>
                      💡 Add your API key to .env file to use this provider
                    </Text>
                  )}
              </Box>
            ),
        )}
      </Box>

      {errorMessage && (
        <Box marginTop={1}>
          <Text
            color={
              errorMessage.includes('Found')
                ? Colors.AccentGreen
                : Colors.AccentRed
            }
          >
            {errorMessage}
          </Text>
        </Box>
      )}
      <Box marginTop={1}>
        <Text color={Colors.Gray}>(Use Enter to select)</Text>
      </Box>
      <Box marginTop={1}>
        <Text>Terms of Services and Privacy Notice for Gemini CLI</Text>
      </Box>
      <Box marginTop={1}>
        <Text color={Colors.AccentBlue}>
          {
            'https://github.com/google-gemini/gemini-cli/blob/main/docs/tos-privacy.md'
          }
        </Text>
      </Box>
    </Box>
  );
}
